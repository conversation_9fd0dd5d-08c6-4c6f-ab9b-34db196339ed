<template>
  <Popup :show="show" @close="handleClose">
    <view class="observation-details-popup">
      <!-- 固定标题栏 -->
      <view class="popup-header">
        <text class="popup-title">观察记录详情</text>
        <uni-icons type="closeempty" size="20" @click="handleClose"></uni-icons>
      </view>

      <!-- 可滚动内容区域 -->
      <scroll-view class="details-content" scroll-y="true" v-if="record">
        <!-- 基本信息 -->
        <view class="detail-section">
          <text class="section-title">基本信息</text>
          <view class="detail-item">
            <text class="detail-label">活动名称：</text>
            <text class="detail-value">{{ record.content.activityName || '无' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">观察地点：</text>
            <text class="detail-value">{{ record.content.observationLocation || '无' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">观察类型：</text>
            <text class="detail-value">{{
              getObservationTypeLabel(record.content.observationType)
            }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">观察时间：</text>
            <text class="detail-value">{{ formatTime(record.content.observationTime) }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">记录时间：</text>
            <text class="detail-value">{{ formatTime(record.recordTime) }}</text>
          </view>
        </view>

        <!-- 观察内容 -->
        <view class="detail-section" v-if="record.content.observationContent">
          <text class="section-title">观察内容</text>
          <view class="observation-content-full">
            <text class="observation-text">{{ record.content.observationContent }}</text>
          </view>
        </view>

        <!-- 图片展示 -->
        <view class="detail-section" v-if="getDisplayImages(record.content.picUrlList).length > 0">
          <text class="section-title">相关图片</text>
          <view
            class="images-grid"
            :class="{
              'single-image': getDisplayImages(record.content.picUrlList).length === 1,
              'two-images': getDisplayImages(record.content.picUrlList).length === 2
            }"
          >
            <view
              v-for="(imageUrl, imageIndex) in getDisplayImages(record.content.picUrlList)"
              :key="imageIndex"
              class="image-item"
              @click="previewImage(imageUrl, getDisplayImages(record.content.picUrlList))"
            >
              <image
                :src="imageUrl"
                mode="aspectFill"
                :lazy-load="true"
                class="detail-image"
              ></image>
            </view>
          </view>
        </view>

        <!-- 分析评价 -->
        <view class="detail-section" v-if="record.content.analysisEvaluation">
          <text class="section-title">分析评价</text>
          <view class="analysis-content-full">
            <text class="analysis-text">{{ record.content.analysisEvaluation }}</text>
          </view>
        </view>

        <!-- AI标识信息 -->
        <view class="detail-section" v-if="record.content.aiFlag !== undefined">
          <text class="section-title">AI信息</text>
          <view class="detail-item">
            <text class="detail-label">AI生成：</text>
            <text class="detail-value">{{ record.content.aiFlag === 1 ? '是' : '否' }}</text>
          </view>
        </view>

        <!-- 其他信息 -->
        <view class="detail-section">
          <text class="section-title">其他信息</text>
          <view class="detail-item">
            <text class="detail-label">状态：</text>
            <text class="detail-value">{{ record.content.state === 1 ? '有效' : '无效' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">创建时间：</text>
            <text class="detail-value">{{ record.content.createdAt || '无' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">更新人：</text>
            <text class="detail-value">{{ record.content.updatedBy || '无' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">更新时间：</text>
            <text class="detail-value">{{ record.content.updatedAt || '无' }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </Popup>
</template>

<script setup>
import Popup from '@/components/Popup/Popup.vue'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close'])

// Methods
const handleClose = () => {
  emit('close')
}

const formatTime = (time) => {
  return time || '无'
}

const getObservationTypeLabel = (type) => {
  const typeMap = {
    Learning: '学习',
    Life: '生活',
    AreaGame: '区域游戏',
    Sports: '体育活动',
    OutdoorGame: '户外自主游戏'
  }
  return typeMap[type] || type || '无'
}

// 处理图片URL字符串，转换为数组并添加OSS处理参数
const getPicUrlArray = (picUrlList) => {
  if (!picUrlList) return []
  return picUrlList
    .split(',')
    .filter((url) => url.trim())
    .map((url) => {
      // 添加OSS图片处理参数，使用较大的尺寸以保证清晰度
      return url + '?x-oss-process=image/resize,m_fill,w_800'
    })
}

// 获取应该显示的图片
const getDisplayImages = (picUrlList) => {
  return getPicUrlArray(picUrlList)
}

// 预览图片
const previewImage = (current, urls) => {
  uni.previewImage({
    current,
    urls
  })
}
</script>

<style lang="scss" scoped>
.observation-details-popup {
  height: 60vh;
  display: flex;
  flex-direction: column;
  padding: 0;

  .popup-header {
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx 30rpx 20rpx 30rpx;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    flex-shrink: 0;

    .popup-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
  }

  .details-content {
    flex: 1;
    padding: 30rpx 0rpx;
    overflow-y: auto;

    .detail-section {
      margin-bottom: 30rpx;

      .section-title {
        display: block;
        font-size: 30rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 20rpx;
        padding-left: 12rpx;
        border-left: 4rpx solid #007aff;
      }

      .detail-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12rpx;
        padding: 12rpx 0;
        border-bottom: 1rpx solid #f8f9fa;

        .detail-label {
          font-size: 26rpx;
          color: #666;
          min-width: 140rpx;
          flex-shrink: 0;
        }

        .detail-value {
          font-size: 26rpx;
          color: #333;
          flex: 1;
          word-break: break-all;
        }
      }

      .observation-content-full {
        background: #f0f7ff;
        padding: 20rpx;
        border-radius: 12rpx;

        .observation-text {
          font-size: 28rpx;
          color: #333;
          line-height: 1.6;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }

      .analysis-content-full {
        background: #f8f9fa;
        padding: 20rpx;
        border-radius: 12rpx;

        .analysis-text {
          font-size: 28rpx;
          color: #333;
          line-height: 1.6;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }

      .images-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 10rpx;
        justify-content: flex-start;

        .image-item {
          border-radius: 8rpx;
          overflow: hidden;
          background: #f5f5f5;

          .detail-image {
            width: 100%;
            height: 100%;
          }
        }

        // 默认3列布局
        .image-item {
          width: calc(33.33% - 7rpx);
          aspect-ratio: 1;
        }

        // 单张图片的样式会通过动态class来控制
        &.single-image .image-item {
          width: 100%;
          aspect-ratio: 16 / 9;
        }

        // 两张图片的样式
        &.two-images .image-item {
          width: calc(50% - 5rpx);
          aspect-ratio: 1;
        }
      }
    }
  }
}
</style>
