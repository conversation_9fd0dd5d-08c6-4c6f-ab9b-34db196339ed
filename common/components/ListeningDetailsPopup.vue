<template>
  <Popup :show="show" @close="handleClose">
    <view class="listening-details-popup">
      <!-- 固定标题栏 -->
      <view class="popup-header">
        <text class="popup-title">倾听记录详情</text>
        <uni-icons type="closeempty" size="20" @click="handleClose"></uni-icons>
      </view>

      <!-- 可滚动内容区域 -->
      <scroll-view class="details-content" scroll-y="true" v-if="record">
        <!-- 基本信息 -->
        <view class="detail-section">
          <text class="section-title">基本信息</text>
          <view class="detail-item">
            <text class="detail-label">标题：</text>
            <text class="detail-value">{{ record.content.title || '无' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">活动地点：</text>
            <text class="detail-value">{{ record.content.activityLocation || '无' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">观察时间：</text>
            <text class="detail-value">{{ formatTime(record.content.observationTime) }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">记录时间：</text>
            <text class="detail-value">{{ formatTime(record.recordTime) }}</text>
          </view>
        </view>

        <!-- 录音文本 -->
        <view class="detail-section" v-if="record.content.audioText">
          <text class="section-title">录音文本</text>
          <view class="audio-text-full">
            <text class="audio-text-content">{{ record.content.audioText }}</text>
          </view>
        </view>

        <!-- 区域图片 -->
        <view class="detail-section" v-if="record.content.areaImg">
          <text class="section-title">区域图片</text>
          <view class="area-image-container">
            <view 
              class="area-image-item"
              @click="previewImage(getProcessedImageUrl(record.content.areaImg), [getProcessedImageUrl(record.content.areaImg)])"
            >
              <image
                :src="getProcessedImageUrl(record.content.areaImg)"
                mode="aspectFill"
                :lazy-load="true"
                class="area-image"
              ></image>
            </view>
          </view>
        </view>

        <!-- 分析评价 -->
        <view class="detail-section" v-if="record.content.analysisEvaluation">
          <text class="section-title">分析评价</text>
          <view class="analysis-content-full">
            <text class="analysis-text">{{ record.content.analysisEvaluation }}</text>
          </view>
        </view>

        <!-- 其他信息 -->
        <view class="detail-section">
          <text class="section-title">其他信息</text>
          <view class="detail-item">
            <text class="detail-label">状态：</text>
            <text class="detail-value">{{ record.content.state === 1 ? '有效' : '无效' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">创建时间：</text>
            <text class="detail-value">{{ record.content.createdAt || '无' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">更新时间：</text>
            <text class="detail-value">{{ record.content.updatedAt || '无' }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </Popup>
</template>

<script setup>
import Popup from '@/components/Popup/Popup.vue'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close'])

// Methods
const handleClose = () => {
  emit('close')
}

const formatTime = (time) => {
  return time || '无'
}

// 处理图片URL，添加OSS处理参数
const getProcessedImageUrl = (imageUrl) => {
  if (!imageUrl) return ''
  return imageUrl + '?x-oss-process=image/resize,m_fill,w_800'
}

// 预览图片
const previewImage = (current, urls) => {
  uni.previewImage({
    current,
    urls
  })
}
</script>

<style lang="scss" scoped>
.listening-details-popup {
  height: 60vh;
  display: flex;
  flex-direction: column;
  padding: 0;

  .popup-header {
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx 30rpx 20rpx 30rpx;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    flex-shrink: 0;

    .popup-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
  }

  .details-content {
    flex: 1;
    padding: 30rpx 0rpx;
    overflow-y: auto;

    .detail-section {
      margin-bottom: 30rpx;

      .section-title {
        display: block;
        font-size: 30rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 20rpx;
        padding-left: 12rpx;
        border-left: 4rpx solid #007aff;
      }

      .detail-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12rpx;
        padding: 12rpx 0;
        border-bottom: 1rpx solid #f8f9fa;

        .detail-label {
          font-size: 26rpx;
          color: #666;
          min-width: 140rpx;
          flex-shrink: 0;
        }

        .detail-value {
          font-size: 26rpx;
          color: #333;
          flex: 1;
          word-break: break-all;
        }
      }

      .audio-text-full {
        background: #f0f7ff;
        padding: 20rpx;
        border-radius: 12rpx;

        .audio-text-content {
          font-size: 28rpx;
          color: #333;
          line-height: 1.6;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }

      .analysis-content-full {
        background: #f8f9fa;
        padding: 20rpx;
        border-radius: 12rpx;

        .analysis-text {
          font-size: 28rpx;
          color: #333;
          line-height: 1.6;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }

      .area-image-container {
        .area-image-item {
          border-radius: 8rpx;
          overflow: hidden;
          background: #f5f5f5;
          width: 100%;
          cursor: pointer;

          .area-image {
            width: 100%;
            height: 300rpx;
            object-fit: cover;
          }
        }
      }
    }
  }
}
</style>
