/*
 * 本页面参数修改后，记得重新启动项目再查看
 * */
const config = {
  DEFINE_ENV: process.env.NODE_ENV,
  // 两个环境，分别是测试、生产
  development: {
    SERVICE_DIR_NAME: "", // 看看我们的网站路径是啥，要不要在网站后面加上比如/web什么的，如果是/web，这里就/web就行了
    GLOBAL_API: "/api",
    BASE_API: "/pts", // 你的接口代理，如果是/api，你就写/api，如果是/webapi，就写/webapi，配置之后，你自己封装接口不需要写这个，正常写就行了
    TRUE_API: "https://api.mypacelab.com", // 这个就是我们实际上后端接口地址，就是我们不带/api的地址，我们使用代理的话就写BASE_API和TRUE_API，不用代理的话，就只配置BASE_API
    JAVA_API_URL: "https://japi.mypacelab.com", // 会议管理相关的请求地址
    WEB_SOCKET_URL: "wss://aihd.mypacelab.com/ai/audio", // 一句话websocket地址
    WEB_URL: "btest.mypacelab.com", // web端
    JAVA_BASE_API: "/api",
    PROJECT_API_URL: "", // 接口真实地址
    TOKEN_NAME: "DEV_TOKEN_KEY",
    BAIDU_SPEECH_APP_ID: 117352963,
    BAIDU_SPEECH_APP_KEY: "3gygHex0tVfzHHdON2w56wRE",
    APPID: "wx81c6c590a0ec7e37",
    WEB_DOMAIN_NAME: "https://pts-source.oss-cn-shenzhen.aliyuncs.com", // 图片域名替换
  },
  production: {
    SERVICE_DIR_NAME: "",
    GLOBAL_API: "/golbal",
    BASE_API: "/pts",
    TRUE_API: "https://api.mypacelab.com",
    JAVA_API_URL: "https://japi.mypacelab.com",
    WEB_SOCKET_URL: "wss://ai.mypacelab.com/ai/audio",
    WEB_URL: "business.mypacelab.com", 
    JAVA_BASE_API: "/api",
    PROJECT_API_URL: "",
    TOKEN_NAME: "PROD_TOKEN_KEY",
    BAIDU_SPEECH_APP_ID: 117352963,
    BAIDU_SPEECH_APP_KEY: "3gygHex0tVfzHHdON2w56wRE",
    APPID: "wx81c6c590a0ec7e37",
    WEB_DOMAIN_NAME: "https://pts-source.oss-cn-shenzhen.aliyuncs.com", // 图片域名替换
  },
};

export default config;
