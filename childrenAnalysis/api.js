import { request } from '@/common/request.js'

// 获取统计数目
export function countAreaEntry(data) {
  // 在 header 添加参数 user_id
  return request({
    url: '/jsapi/business/childAreaEntry/countAreaEntry',
    method: 'POST',
    headers: {
      user_id: uni.getStorageSync('USER_INFO').userId
    },
    data
  })
}

// /business/childAreaEntry/listAreaEntryCountByWeek 获取该班级本学期进区统计数据（按区域-每周） get  Query 参数 classId integer
export function listAreaEntryCountByWeek(data) {
  return request({
    url: '/jsapi/business/childAreaEntry/listAreaEntryCountByWeek',
    method: 'GET',
    data
  })
}

// /business/childAreaEntry/countAreaEntryByChildId
export function countAreaEntryByChildId(data) {
  return request({
    url: '/jsapi/business/childAreaEntry/countAreaEntryByChildId',
    method: 'POST',
    data
  })
}
