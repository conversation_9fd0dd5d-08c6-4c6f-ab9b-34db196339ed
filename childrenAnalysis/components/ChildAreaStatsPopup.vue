<template>
  <Popup :show="show" @close="handleClose">
    <view class="child-stats-popup">
      <view class="popup-header">
        <text class="popup-title">{{ childData.title }}的进区统计</text>
        <text class="close-btn" @click="handleClose">×</text>
      </view>

      <!-- Tab 切换 -->
      <view class="tab-container">
        <view
          class="tab-item"
          :class="{ active: activeTab === 'radar' }"
          @click="switchTab('radar')"
        >
          雷达图
        </view>
        <view
          v-if="showTimelineTab"
          class="tab-item"
          :class="{ active: activeTab === 'timeline' }"
          @click="switchTab('timeline')"
        >
          时间轴
        </view>
      </view>

      <view class="popup-content">
        <!-- 雷达图 Tab -->
        <view v-if="activeTab === 'radar'" class="radar-tab">
          <view class="charts-box">
            <view
              v-if="radarChartData.categories && radarChartData.categories.length > 0"
              class="chart-container"
            >
              <qiun-data-charts
                type="radar"
                :opts="radarOpts"
                :chartData="radarChartData"
                @onChartReady="onRadarChartReady"
              />
            </view>
            <view v-else class="chart-empty">
              <text>暂无区域数据</text>
            </view>
          </view>
        </view>

        <!-- 时间轴 Tab -->
        <view v-if="activeTab === 'timeline'" class="timeline-tab">
          <!-- 筛选条件 -->
          <view class="filter-section">
            <view class="filter-row">
              <text class="filter-label">时间类型：</text>
              <view class="filter-options">
                <view
                  class="filter-option"
                  :class="{ active: selectedDateType === 'today' }"
                  @click="changeDateType('today')"
                >
                  今日
                </view>
                <view
                  class="filter-option"
                  :class="{ active: selectedDateType === 'yesterday' }"
                  @click="changeDateType('yesterday')"
                >
                  昨日
                </view>
                <view
                  class="filter-option"
                  :class="{ active: selectedDateType === 'thisWeek' }"
                  @click="changeDateType('thisWeek')"
                >
                  本周
                </view>
                <view
                  class="filter-option"
                  :class="{ active: selectedDateType === 'semester' }"
                  @click="changeDateType('semester')"
                >
                  本学期
                </view>
              </view>
            </view>

            <!-- 自定义日期范围 -->
            <view class="filter-row">
              <text class="filter-label">日期范围：</text>
              <view class="date-range-container">
                <uni-datetime-picker
                  type="daterange"
                  v-model="customDateRange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleCustomDateRange"
                  :border="false"
                  :clear-icon="false"
                >
                  <template v-slot:default>
                    <view class="custom-date-input">
                      {{ formatCustomDateRange }}
                    </view>
                  </template>
                </uni-datetime-picker>
              </view>
            </view>
          </view>

          <view class="charts-box">
            <view v-if="isLoading" class="chart-loading">
              <text>正在加载数据...</text>
            </view>
            <view
              v-else-if="scatterChartData.series && scatterChartData.series.length > 0"
              class="chart-container"
            >
              <qiun-data-charts
                type="scatter"
                :opts="scatterOpts"
                :chartData="scatterChartData"
                @onChartReady="onScatterChartReady"
              />
            </view>
            <view v-else class="chart-empty">
              <text>{{ props.childData.title || '该儿童' }}在{{ formatCurrentDateRange }}期间暂无进区记录</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </Popup>
</template>

<script setup>
import Popup from '@/components/Popup/Popup.vue'
import QiunDataCharts from '../qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'
import { countAreaEntryByChildId } from '../api'
import { ref, watch, computed } from 'vue'
import dayjs from 'dayjs'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  childData: {
    type: Object,
    default: () => ({})
  },
  areaList: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['close'])

// 响应式数据
const activeTab = ref('radar') // 默认显示雷达图
const radarChartData = ref({})
const scatterChartData = ref({})
const radarChartInstance = ref(null)
const scatterChartInstance = ref(null)
const isLoading = ref(false)
const selectedDateType = ref('semester') // 默认本学期
const customDateRange = ref([]) // 自定义日期范围

// 雷达图配置
const radarOpts = ref({
  color: ['#1890FF'],
  enableScroll: false,
  legend: {
    show: false
  },
  dataPointShape: true,
  extra: {
    radar: {
      gridType: 'circle',
      gridColor: '#E6E6E6',
      labelShow: true,
      opacity: 0.3,
      radius: 100,
      labelShow: true,
      border: true,
      borderWidth: 1,
      labelColor: '#666666',
      borderColor: '#E6E6E6'
    }
  }
})

// 散点图配置
const scatterOpts = ref({
  color: [
    '#1890FF',
    '#91CB74',
    '#FAC858',
    '#EE6666',
    '#73C0DE',
    '#3CA272',
    '#FC8452',
    '#9A60B4',
    '#ea7ccc'
  ],
  padding: [15, 15, 0, 15],
  dataLabel: false,
  enableScroll: false,
  legend: {
    show: false
  },
  xAxis: {
    disableGrid: false,
    gridType: 'dash',
    axisLabel: true,
    axisLabelColor: '#666666',
    fontSize: 10,
    type: 'categories',
    categories: [] // 将在数据生成时动态设置日期
  },
  yAxis: {
    data: [
      {
        type: 'categories',
        categories: [], // 将在数据生成时动态设置区域名称
        disableGrid: false,
        gridType: 'dash',
        axisLabel: true,
        axisLabelColor: '#666666',
        fontSize: 10,
        fontColor: '#666666'
      }
    ]
  },
  extra: {
    scatter: {
      pointSize: 8,
      opacity: 0.8
    }
  }
})

// 计算属性
const showTimelineTab = computed(() => {
  return props.childData && props.childData.id
})

const formatCustomDateRange = computed(() => {
  if (customDateRange.value && customDateRange.value.length === 2) {
    const startDate = dayjs(customDateRange.value[0]).format('YYYY.MM.DD')
    const endDate = dayjs(customDateRange.value[1]).format('YYYY.MM.DD')
    return `${startDate} 至 ${endDate}`
  }
  return '请选择日期范围'
})

const formatCurrentDateRange = computed(() => {
  if (selectedDateType.value === 'custom' && customDateRange.value && customDateRange.value.length === 2) {
    const startDate = dayjs(customDateRange.value[0]).format('YYYY-MM-DD')
    const endDate = dayjs(customDateRange.value[1]).format('YYYY-MM-DD')
    return `${startDate}至${endDate}`
  }

  const dateTypeMap = {
    today: '今日',
    yesterday: '昨日',
    dayBeforeYesterday: '前日',
    thisWeek: '本周',
    lastWeek: '上周',
    last7Days: '最近7天',
    thisMonth: '本月',
    lastMonth: '上月',
    last30Days: '最近30天',
    semester: '本学期'
  }

  return dateTypeMap[selectedDateType.value] || '本学期'
})

// Tab 切换
const switchTab = async (tab) => {
  activeTab.value = tab
  if (tab === 'radar') {
    generateRadarData()
  } else if (tab === 'timeline') {
    await getTimelineData()
  }
}

// 切换日期类型
const changeDateType = async (dateType) => {
  if (selectedDateType.value === dateType) return
  selectedDateType.value = dateType
  // 清空自定义日期范围
  customDateRange.value = []
  await getTimelineData()
}

// 处理自定义日期范围选择
const handleCustomDateRange = async (e) => {
  if (e && e.length === 2) {
    customDateRange.value = e
    selectedDateType.value = 'custom'
    await getTimelineData()
  }
}

// 关闭弹窗
const handleClose = () => {
  // 清理图表实例
  if (radarChartInstance.value) {
    try {
      radarChartInstance.value.dispose && radarChartInstance.value.dispose()
    } catch (error) {
      console.warn('清理雷达图实例时出错:', error)
    }
    radarChartInstance.value = null
  }

  if (scatterChartInstance.value) {
    try {
      scatterChartInstance.value.dispose && scatterChartInstance.value.dispose()
    } catch (error) {
      console.warn('清理散点图实例时出错:', error)
    }
    scatterChartInstance.value = null
  }

  // 清空数据
  radarChartData.value = {}
  scatterChartData.value = {}
  activeTab.value = 'radar'

  emit('close')
}

// 生成雷达图数据
const generateRadarData = () => {
  if (!props.areaList || props.areaList.length === 0) {
    radarChartData.value = {}
    return
  }

  // 获取该儿童在各区域的进区次数，确保即使是0也要显示
  const childData = props.areaList.map((area) => {
    // 兼容两种数据结构：按学生统计和按周次统计
    const entryCountList = props.childData.childAreaEntryCountList || props.childData.arreaEntryCountList

    if (!entryCountList) {
      return 0
    }
    const areaEntry = entryCountList.find(
      (entry) => entry.area === area.area
    )
    return areaEntry ? areaEntry.count : 0
  })

  // 找到当前儿童进区次数最多的数值
  const currentChildMaxCount = Math.max(...childData)

  // 如果当前儿童所有数据都是0，设置默认值为10
  const displayMaxCount = currentChildMaxCount === 0 ? 10 : currentChildMaxCount + 0.5

  // 获取所有区域名称并在后面添加该儿童在该区域的实际进区次数
  const categories = props.areaList.map((area, index) => {
    const count = childData[index]
    return `${area.area}(${count})`
  })

  // 构造雷达图数据
  radarChartData.value = {
    categories: categories,
    series: [
      {
        name: props.childData.title,
        data: childData
      }
    ]
  }

  // 设置雷达图最大值
  radarOpts.value.extra.radar.max = displayMaxCount
}

// 获取时间轴数据
const getTimelineData = async () => {
  // 检查是否有儿童ID
  if (!props.childData.id) {
    scatterChartData.value = {
      categories: [],
      series: []
    }
    return
  }

  isLoading.value = true

  try {
    const params = {
      childId: props.childData.id
      // childId: 2166
    }

    // 根据选择的日期类型添加参数
    if (selectedDateType.value === 'custom') {
      if (customDateRange.value && customDateRange.value.length === 2) {
        params.startDate = dayjs(customDateRange.value[0]).format('YYYY-MM-DD')
        params.endDate = dayjs(customDateRange.value[1]).format('YYYY-MM-DD')
      }
    } else {
      params.dateType = selectedDateType.value
    }

    const res = await countAreaEntryByChildId(params)

    if (res.status === 0 && res.data && Array.isArray(res.data)) {
      generateScatterData(res.data)
    } else {
      scatterChartData.value = {
        categories: [],
        series: []
      }
    }
  } catch (error) {
    console.error('获取时间轴数据失败', error)
    scatterChartData.value = {
      categories: [],
      series: []
    }
  } finally {
    isLoading.value = false
  }
}

// 生成散点图数据
const generateScatterData = (data) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    scatterChartData.value = {
      series: []
    }
    // 清空坐标轴分类
    scatterOpts.value.xAxis.categories = []
    scatterOpts.value.yAxis.data[0].categories = []
    return
  }

  // 获取所有唯一的日期，并按日期排序
  const uniqueDates = [...new Set(data.map((item) => item.date))].sort()

  // 获取所有唯一的区域
  const uniqueAreas = [...new Set(data.map((item) => item.area))]

  // 格式化日期显示（只显示月-日）
  const formattedDates = uniqueDates.map((date) => {
    const dateObj = new Date(date)
    return `${dateObj.getMonth() + 1}-${dateObj.getDate()}`
  })

  // 设置 x 轴日期分类
  scatterOpts.value.xAxis.categories = formattedDates

  // 设置 y 轴区域分类
  scatterOpts.value.yAxis.data[0].categories = uniqueAreas

  // 为每个区域创建一个系列
  const series = uniqueAreas.map((area, areaIndex) => {
    const areaData = []

    // 遍历所有日期，为该区域生成散点数据
    uniqueDates.forEach((date, dateIndex) => {
      // 查找该日期该区域的数据
      const record = data.find((item) => item.date === date && item.area === area)
      if (record && record.count > 0) {
        // x轴是日期索引，y轴是区域索引
        areaData.push([dateIndex, areaIndex])
      }
    })

    return {
      name: area,
      data: areaData
    }
  }).filter(series => series.data.length > 0) // 只保留有数据的系列

  scatterChartData.value = {
    series: series
  }
}

// 图表就绪回调
const onRadarChartReady = (chart) => {
  radarChartInstance.value = chart
}

const onScatterChartReady = (chart) => {
  scatterChartInstance.value = chart
}

// 监听 props 变化
watch(
  () => props.show,
  async (newVal) => {
    if (newVal) {
      // 如果没有 childId，强制切换到雷达图
      if (!showTimelineTab.value && activeTab.value === 'timeline') {
        activeTab.value = 'radar'
      }

      // 弹窗打开时生成对应的图表数据
      if (activeTab.value === 'radar') {
        generateRadarData()
      } else {
        await getTimelineData()
      }
    }
  }
)

watch(
  () => props.childData,
  async () => {
    if (props.show) {
      // 如果没有 childId，强制切换到雷达图
      if (!showTimelineTab.value && activeTab.value === 'timeline') {
        activeTab.value = 'radar'
      }

      if (activeTab.value === 'radar') {
        generateRadarData()
      } else {
        await getTimelineData()
      }
    }
  }
)
</script>

<style scoped lang="scss">
.child-stats-popup {
  max-height: 80vh;
  background: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      font-size: 48rpx;
      color: #999;
      font-weight: 300;
      line-height: 1;
      padding: 10rpx;

      &:active {
        color: #666;
      }
    }
  }

  .tab-container {
    display: flex;
    border-bottom: 1rpx solid #f0f0f0;

    .tab-item {
      flex: 1;
      padding: 24rpx;
      text-align: center;
      font-size: 28rpx;
      color: #666;
      background: #f8f8f8;
      transition: all 0.3s ease;

      &.active {
        color: #1890ff;
        background: #fff;
        border-bottom: 2rpx solid #1890ff;
      }

      &:active {
        background: #f0f0f0;
      }
    }
  }

  .popup-content {
    .radar-tab,
    .timeline-tab {
      .charts-box {
        // width: 100%;
        height: 500rpx;
        padding: 20rpx;

        .chart-container {
          width: 100%;
          height: 100%;
          position: relative;
        }

        .chart-empty,
        .chart-loading {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 100%;
          color: #999;
          font-size: 28rpx;
        }
      }
    }

    .timeline-tab {
      .filter-section {
        padding: 20rpx;
        border-bottom: 1rpx solid #f0f0f0;
        background: #fafafa;

        .filter-row {
          display: flex;
          align-items: center;
          margin-bottom: 16rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .filter-label {
            font-size: 26rpx;
            color: #333;
            margin-right: 16rpx;
            min-width: 120rpx;
          }

          .filter-options {
            display: flex;
            gap: 12rpx;
            flex-wrap: wrap;

            .filter-option {
              padding: 8rpx 16rpx;
              background: #f5f5f5;
              border-radius: 6rpx;
              font-size: 24rpx;
              color: #666;
              transition: all 0.3s ease;

              &.active {
                background: #1890FF;
                color: #fff;
              }

              &:active {
                transform: scale(0.95);
              }
            }
          }

          .date-range-container {
            flex: 1;

            .custom-date-input {
              padding: 8rpx 16rpx;
              background: #f5f5f5;
              border-radius: 6rpx;
              font-size: 24rpx;
              color: #333;
              text-align: center;
              min-height: 40rpx;
              line-height: 40rpx;
            }
          }
        }
      }
    }
  }
}
</style>
