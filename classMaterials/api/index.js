import { request } from '@/common/request.js'

// 获取推荐材料列表 /business/material/getRecommendMaterial
export function getRecommendMaterial(data) {
  return request({
    url: `/jsapi/business/material/getRecommendMaterial`,
    method: 'POST',
    data
  })
}

// /business/material/exportMaterial/{recommendType} 导出excel recommendType
// Path 参数 recommendType: 'purchase' | 'requisition'
// Body 参数: Array 列表项数组
export function exportMaterial(recommendType, data) {
  return request({
    url: `/jsapi/business/material/exportMaterial/${recommendType}`,
    method: 'POST',
    responseType: 'arraybuffer',
    data
  })
}

// 采购材料推荐 材料详情 /global/material/queryMaterialDetail
export function queryMaterialDetail(data) {
  return request({
    url: `/jsapi/global/material/queryMaterialDetail`,
    method: 'GET',
    data
  })
}
// 采购材料推荐 材料分析 /global/material/getMaterialAnalysis 参数 combinedId
export function getMaterialAnalysisNew(data) {
  return request({
    url: `/jsapi/global/material/getMaterialAnalysis`,
    method: 'GET',
    data
  })
}
// 采购材料推荐 已有玩法 /global/material/getMaterialPlayList 参数 combinedId
export function getMaterialPlayListNew(data) {
  return request({
    url: `/jsapi/global/material/getMaterialPlayList`,
    method: 'GET',
    data
  })
}
