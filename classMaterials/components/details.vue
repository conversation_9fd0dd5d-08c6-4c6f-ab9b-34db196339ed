<template>
  <BaseLayout
    :nav-title="detailType === 'book' ? '图书详情' : '材料详情'"
    :content-style="{ padding: '0' }"
    containerClass="cirBaseLayout"
  >
    <view class="main-container">
      <!-- 图书基本信息 -->
      <view class="row basicInfo" v-if="detailType === 'book'">
        <image
          src="/static/game/edit.svg"
          class="edit_icon"
          @click.stop="openEditPopup('basicInfo')"
        ></image>
        <view class="top_box">
          <view class="avatar">
            <image
              :src="basicInfoData.selectedImg.split(',')[0]"
              mode="aspectFill"
              @click="
                previewImage(
                  basicInfoData.selectedImg.split(',')[0],
                  basicInfoData.selectedImg.split(',')
                )
              "
            ></image>
          </view>
          <view class="name">
            <view>{{ basicInfoData.name }}</view>
            <view>{{ basicInfoData.area }}</view>
          </view>
        </view>
        <view class="center_box">
          <view>图书名称：{{ basicInfoData.name }}</view>
        </view>
        <!-- <view class="btm_box">
          <scroll-view class="scroll-container" scroll-x="true" show-scrollbar="false">
            <view class="images-wrapper" v-if="basicInfoData.selectedImg">
              <image
                class="pic"
                v-for="(img, i) of basicInfoData.selectedImg.split(',')"
                :key="i"
                :src="img"
                mode="aspectFill"
                @click="previewImage(img, basicInfoData.selectedImg.split(','))"
              ></image>
            </view>
          </scroll-view>
        </view> -->
      </view>
      <!-- 基本信息 -->
      <view class="row basicInfo" v-if="detailType === 'toy'">
        <image
          src="/static/game/edit.svg"
          class="edit_icon"
          @click.stop="openEditPopup('basicInfo')"
        ></image>
        <view class="top_box">
          <view class="avatar">
            <image
              :src="basicInfoData.selectedImg.split(',')[0]"
              mode="aspectFill"
              @click="
                previewImage(
                  basicInfoData.selectedImg.split(',')[0],
                  basicInfoData.selectedImg.split(',')
                )
              "
            ></image>
          </view>
          <view class="name">
            <view>{{ basicInfoData.name }}</view>
            <view>{{ basicInfoData.area }}</view>
          </view>
        </view>
        <view class="center_box">
          <!-- <view>材料名称：{{ basicInfoData.taobaoName }}</view> -->
          <view>制作方式：{{ basicInfoData.productionMethod }}</view>
          <view>材质：{{ basicInfoData.materialType }}</view>
          <view>品质：{{ basicInfoData.qualityLevel }}</view>
          <view>安全性：{{ basicInfoData.safetyLevel }}</view>
          <view>重量（克）：{{ basicInfoData.weightGram }}</view>
        </view>
        <view class="btm_box">
          <scroll-view class="scroll-container" scroll-x="true" show-scrollbar="false">
            <view class="images-wrapper" v-if="basicInfoData.selectedImg">
              <image
                class="pic"
                v-for="(img, i) of basicInfoData.selectedImg.split(',')"
                :key="i"
                :src="img"
                mode="aspectFill"
                @click="previewImage(img, basicInfoData.selectedImg.split(','))"
              ></image>
            </view>
          </scroll-view>
        </view>
      </view>
      <!-- 材料分析 -->
      <view class="row materialAnalysis" v-if="detailType === 'toy'">
        <image
          src="/static/game/edit.svg"
          class="edit_icon"
          @click.stop="openEditPopup('materialAnalysis')"
        ></image>
        <view class="title">
          <text>材料分析</text>
          <image class="text_bg" src="/static/game/text_bg.svg"></image>
        </view>
        <view class="content-wrapper" :class="{ 'content-collapsed': !isExpanded }">
          <view>
            <view class="sub_title"> 材料概述 </view>
            <view> {{ materialAnalysisData.materialSummary }} </view>
          </view>
          <view>
            <view class="sub_title"> 投放目的 </view>
            <view>
              {{ materialAnalysisData.purpose }}
            </view>
          </view>
          <view>
            <view class="sub_title"> 适合年龄段 </view>
            <view> {{ materialAnalysisData.minAge }}岁 - {{ materialAnalysisData.maxAge }}岁 </view>
          </view>
          <view>
            <view class="sub_title"> 适宜人数 </view>
            <view>
              {{ materialAnalysisData.suitablePeopleMin }}人 -
              {{ materialAnalysisData.suitablePeopleMax }}人
            </view>
          </view>
        </view>
        <view class="toggle-btn" @click="toggleExpand" :class="{ collapsed: isExpanded }">
          {{ isExpanded ? '收起' : '展开' }}
          <image v-if="isExpanded" class="toggle-icon" src="/static/game/up.svg"></image>
          <image
            v-else
            class="toggle-icon"
            style="transform: rotate(180deg)"
            src="/static/game/up.svg"
          ></image>
        </view>
      </view>
      <!-- 已采纳玩法 -->
      <view class="row appliedPlay" v-if="detailType === 'toy'">
        <view class="title">
          <text>已采纳玩法</text>
          <image class="text_bg" src="/static/game/text_bg.svg"></image>
        </view>
        <view class="play-list">
          <view
            class="play-item"
            v-for="(item, index) of playList"
            :key="index"
            @click="openPlayDetailPopup(true, index)"
          >
            <view class="play-item-content">
              <view class="play-title-row">
                <view class="play-title">{{ item.playName }}</view>
                <view
                  class="play-interest-tag"
                  v-if="item.childInterest"
                  :class="{
                    'very-low': item.childInterest == getInterestCodeByName('非常不感兴趣'),
                    low: item.childInterest == getInterestCodeByName('不感兴趣'),
                    medium: item.childInterest == getInterestCodeByName('一般感兴趣'),
                    high: item.childInterest == getInterestCodeByName('比较感兴趣'),
                    'very-high': item.childInterest == getInterestCodeByName('非常感兴趣')
                  }"
                >
                  {{ getChildInterestName(item.childInterest) }}
                </view>
              </view>
              <view class="play-intro">
                {{ item.playIntro }}
              </view>
            </view>
            <up-icon name="arrow-right"></up-icon>
          </view>
        </view>
      </view>
      <!-- AI推荐玩法 -->
      <view class="row aiRecommendPlay" v-if="detailType === 'toy'">
        <view class="title">
          <text>AI推荐玩法</text>
          <image class="text_bg" src="/static/game/text_bg.svg"></image>

          <view @click="openAIRecommendPopup" class="recommend-btn">重新推荐</view>
        </view>
        <view class="play-list">
          <view
            class="play-item"
            v-for="(item, index) of aiPlayList"
            :key="index"
            @click="openPlayDetailPopup(false, index)"
          >
            <view class="play-item-content">
              <view class="play-title-row">
                <view class="play-title">{{ item.playName }}</view>
              </view>
              <view class="play-intro">
                {{ item.playIntro }}
              </view>
            </view>
            <up-icon name="arrow-right"></up-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 编辑基本信息弹窗 -->
    <Popup
      :show="showBasicInfoEditPopup"
      @close="closeEditPopup"
      mode="bottom"
      round="10"
      closeable
      safeAreaInsetBottom
    >
      <view class="edit-popup-container">
        <view class="edit-form-header">
          <view class="add-form-title">
            <view>编辑基本信息</view>
            <view @click="saveEdit('basicInfo')">完成</view>
          </view>
        </view>
        <scroll-view scroll-y="true" class="edit-form-scroll">
          <view class="edit-form-content">
            <view style="padding: 0 20rpx">
              <up-form
                ref="basicInfoFormRef"
                labelPosition="left"
                :model="editBasicInfoData"
                labelWidth="140"
              >
                <up-form-item label="区域" prop="area" :style="formItemStyle">
                  <view class="custom-select" @click="openRegionPopup">
                    <text class="select-text">{{ tempRegionName || '请选择区域' }}</text>
                    <up-icon name="arrow-right" />
                  </view>
                </up-form-item>
                <up-form-item label="名称" prop="name" :style="formItemStyle">
                  <up-input
                    v-model="editBasicInfoData.name"
                    placeholder="请输入名称"
                    border="none"
                  />
                </up-form-item>
                <up-form-item label="制作方式" prop="productionmethod" :style="formItemStyle">
                  <view class="custom-select" @click="openProductionMethodPopup">
                    <text class="select-text">{{
                      getProductionMethodName(editBasicInfoData.productionmethod) ||
                      '请选择制作方式'
                    }}</text>
                    <up-icon name="arrow-right" />
                  </view>
                </up-form-item>
                <up-form-item label="材质" prop="materialtype" :style="formItemStyle">
                  <up-input
                    v-model="editBasicInfoData.materialtype"
                    placeholder="请输入材质"
                    border="none"
                  />
                </up-form-item>
                <up-form-item label="品质" prop="qualitylevel" :style="formItemStyle">
                  <view class="custom-select" @click="openQualityPopup">
                    <text class="select-text">{{
                      getQualityName(editBasicInfoData.qualitylevel) || '请选择品质'
                    }}</text>
                    <up-icon name="arrow-right" />
                  </view>
                </up-form-item>
                <up-form-item label="安全性" prop="safetylevel" :style="formItemStyle">
                  <view class="custom-select" @click="openSafetyPopup">
                    <text class="select-text">{{
                      getSafetyName(editBasicInfoData.safetylevel) || '请选择安全性'
                    }}</text>
                    <up-icon name="arrow-right" />
                  </view>
                </up-form-item>
                <up-form-item label="重量(克)" prop="weightgram" :style="formItemStyle">
                  <up-input
                    v-model="editBasicInfoData.weightgram"
                    placeholder="请输入重量"
                    border="none"
                  />
                </up-form-item>
              </up-form>
            </view>

            <!-- 图片管理区域 -->
            <view class="image-management-section">
              <view class="image-management-title">
                <text>图片管理</text>
                <text class="image-hint">首图将作为材料头像</text>
              </view>

              <view class="image-list">
                <view
                  v-for="(img, index) in editBasicInfoData.selectedImg.split(',')"
                  :key="index"
                  class="image-item"
                >
                  <image :src="img" mode="aspectFill" class="preview-image"></image>
                  <view class="image-actions">
                    <view
                      class="action-btn move-up"
                      v-if="index > 0"
                      @click="moveImage(index, 'up')"
                    >
                      <up-icon name="arrow-up" size="20" color="#fff"></up-icon>
                    </view>
                    <view
                      class="action-btn move-down"
                      v-if="index < editBasicInfoData.selectedImg.split(',').length - 1"
                      @click="moveImage(index, 'down')"
                    >
                      <up-icon name="arrow-down" size="20" color="#fff"></up-icon>
                    </view>
                    <view class="action-btn delete" @click="deleteImage(index)">
                      <up-icon name="trash" size="20" color="#fff"></up-icon>
                    </view>
                  </view>
                </view>

                <!-- 添加图片按钮 -->
                <UploadNew
                  :value="[]"
                  @callback="uploadCallback"
                  @emitDelFile="delFile"
                  :showDel="true"
                  :maxCount="9"
                  type="image"
                >
                  <view class="upload-icon-wrapper">
                    <up-icon name="plus" size="40rpx" color="#999999"></up-icon>
                    <text class="upload-text">添加图片</text>
                  </view>
                </UploadNew>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </Popup>

    <!-- 图书编辑弹窗 -->
    <Popup
      :show="showBookEditPopup"
      @close="closeEditPopup"
      mode="bottom"
      round="10"
      closeable
      safeAreaInsetBottom
    >
      <view class="edit-popup-container">
        <view class="edit-form-header">
          <view class="add-form-title">
            <view>编辑图书信息</view>
            <view @click="saveEdit('basicInfo')">完成</view>
          </view>
        </view>
        <scroll-view scroll-y="true" class="edit-form-scroll">
          <view class="edit-form-content">
            <view style="padding: 0 20rpx">
              <up-form
                ref="bookFormRef"
                labelPosition="left"
                :model="editBookInfoData"
                labelWidth="140"
              >
                <up-form-item label="图书名称" prop="name" :style="formItemStyle">
                  <up-input
                    v-model="editBookInfoData.name"
                    placeholder="请输入图书名称"
                    border="none"
                  />
                </up-form-item>
                <up-form-item label="区域" prop="area" :style="formItemStyle">
                  <view class="custom-select" @click="openRegionPopup">
                    <text class="select-text">{{ tempRegionName || '请选择区域' }}</text>
                    <up-icon name="arrow-right" />
                  </view>
                </up-form-item>
              </up-form>
            </view>

            <!-- 封面图片管理区域 -->
            <view class="image-management-section">
              <view class="image-management-title">
                <text>封面图片</text>
                <text class="image-hint">首图将作为图书封面</text>
              </view>

              <view class="image-list">
                <view
                  v-for="(img, index) in editBookInfoData.selectedImg
                    ? editBookInfoData.selectedImg.split(',').filter((img) => img.trim())
                    : []"
                  :key="index"
                  class="image-item"
                >
                  <image :src="img" mode="aspectFill" class="preview-image"></image>
                  <view class="image-actions">
                    <view
                      class="action-btn move-up"
                      v-if="index > 0"
                      @click="moveBookImage(index, 'up')"
                    >
                      <up-icon name="arrow-up" size="20" color="#fff"></up-icon>
                    </view>
                    <view
                      class="action-btn move-down"
                      v-if="
                        index <
                        (editBookInfoData.selectedImg
                          ? editBookInfoData.selectedImg.split(',').filter((img) => img.trim())
                              .length - 1
                          : 0)
                      "
                      @click="moveBookImage(index, 'down')"
                    >
                      <up-icon name="arrow-down" size="20" color="#fff"></up-icon>
                    </view>
                    <view class="action-btn delete" @click="deleteBookImage(index)">
                      <up-icon name="trash" size="20" color="#fff"></up-icon>
                    </view>
                  </view>
                </view>

                <!-- 添加图片按钮 -->
                <UploadNew
                  :value="[]"
                  @callback="bookUploadCallback"
                  @emitDelFile="bookDelFile"
                  :showDel="true"
                  :maxCount="5"
                  type="image"
                >
                  <view class="upload-icon-wrapper">
                    <up-icon name="plus" size="40rpx" color="#999999"></up-icon>
                    <text class="upload-text">添加图片</text>
                  </view>
                </UploadNew>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </Popup>

    <!-- 品质选择弹窗 -->
    <up-picker
      :show="showQualityPicker"
      :columns="qualityColumns"
      @cancel="closeQualityPopup"
      @confirm="confirmQualityPicker"
      title="选择品质"
    ></up-picker>

    <!-- 安全性选择弹窗 -->
    <up-picker
      :show="showSafetyPicker"
      :columns="safetyColumns"
      @cancel="closeSafetyPopup"
      @confirm="confirmSafetyPicker"
      title="选择安全性"
    ></up-picker>

    <!-- 制作方式选择弹窗 -->
    <up-picker
      :show="showProductionMethodPicker"
      :columns="productionMethodColumns"
      @cancel="closeProductionMethodPopup"
      @confirm="confirmProductionMethodPicker"
      title="选择制作方式"
    ></up-picker>

    <!-- 区域选择弹窗 -->
    <up-picker
      :show="showRegionPicker"
      :columns="regionColumns"
      @cancel="showRegionPicker = false"
      @confirm="confirmRegion"
      title="选择区域"
    ></up-picker>

    <!-- AI推荐弹窗 -->
    <AIRecommendPopup
      :show="showAIRecommendPopup"
      :materialId="materialId"
      @close="closeAIRecommendPopup"
      @generate-success="handleAIGenerateSuccess"
    />

    <!-- 玩法详情弹窗 -->
    <up-popup
      :show="showPlayDetailPopup"
      @close="closePlayDetailPopup"
      mode="bottom"
      :round="10"
      :closeable="true"
      closeIconPos="top-right"
      safeAreaInsetBottom
    >
      <view class="play-detail-popup-container">
        <scroll-view
          scroll-y="true"
          class="play-detail-popup"
          :scroll-with-animation="true"
          :enhanced="true"
          :show-scrollbar="false"
        >
          <view class="play-detail-header">
            <view class="play-detail-title">
              <template v-if="isEditMode">
                <input
                  type="text"
                  v-model="editingPlayDetail.title"
                  class="editable-field play-title-edit"
                />
              </template>
              <template v-else>
                {{ currentPlayDetail.title }}
              </template>
            </view>
          </view>

          <view class="play-detail-content">
            <!-- 状态显示 -->
            <view class="play-detail-status">
              <text>状态：</text>
              <text>{{ currentPlayDetail.isAdopted ? '已采用' : '未采用' }}</text>
            </view>

            <!-- 使用up-form组件进行编辑 -->
            <template v-if="isEditMode">
              <up-form
                ref="playDetailFormRef"
                labelPosition="top"
                :model="editingPlayDetail"
                labelWidth="180"
                labelStyle="{fontSize: 30rpx; fontWeight: 600;}"
                labelAlign="left"
              >
                <!-- 基本信息 -->
                <up-form-item :style="detailFormItemStyle" label="适合年龄段" prop="ageRange">
                  <up-input
                    v-model="editingPlayDetail.ageRange"
                    placeholder="请输入适合年龄段"
                    border="none"
                    :cursorSpacing="100"
                  />
                </up-form-item>

                <up-form-item :style="detailFormItemStyle" label="投放区域" prop="area">
                  <up-input
                    v-model="editingPlayDetail.area"
                    placeholder="请输入投放区域"
                    border="none"
                    :cursorSpacing="100"
                  />
                </up-form-item>

                <up-form-item :style="detailFormItemStyle" label="适宜人数" prop="peopleCount">
                  <up-input
                    v-model="editingPlayDetail.peopleCount"
                    placeholder="请输入适宜人数"
                    border="none"
                    :cursorSpacing="100"
                  />
                </up-form-item>

                <up-form-item :style="detailFormItemStyle" label="结构化" prop="structure">
                  <up-input
                    v-model="editingPlayDetail.structure"
                    placeholder="请输入结构化"
                    border="none"
                    :cursorSpacing="100"
                  />
                </up-form-item>

                <!-- 玩法介绍 -->
                <up-form-item :style="detailFormItemStyle" label="玩法介绍" prop="introduction">
                  <up-textarea
                    v-model="editingPlayDetail.introduction"
                    placeholder="请输入玩法介绍"
                    border="none"
                    :cursorSpacing="100"
                    autoHeight
                    confirmType="none"
                    :maxlength="-1"
                  />
                </up-form-item>

                <!-- 核心经验 -->
                <up-form-item :style="detailFormItemStyle" label="核心经验" prop="experiences">
                  <!-- 已有的核心经验 -->
                  <view v-for="(item, index) in coreExperienceList" :key="index" class="hxjy">
                    <view class="hxjy_title flex-ac">
                      <view class="line"></view>
                      <text v-if="item.isExisting" class="hxjy_title_text"
                        >已有经验{{ item.experienceIndex + 1 }}</text
                      >
                      <text v-else class="hxjy_title_text">新增经验</text>
                      <image
                        class="delete_hxjy"
                        src="@/static/common/delete.png"
                        @click="removeCoreExperience(index)"
                      />
                    </view>

                    <!-- 已有经验显示 -->
                    <view v-if="item.isExisting" class="existing-experience">
                      {{ item.experienceText }}
                    </view>

                    <!-- 新增经验选择 -->
                    <template v-else>
                      <uni-data-select
                        v-model="item.selectedIds[0]"
                        :localdata="item.options[0]"
                        @change="(val) => onSelectCoreExp(0, val, index)"
                        style="margin-bottom: 15rpx"
                        clear
                        placeholder="请选择领域"
                      />
                      <uni-data-select
                        v-model="item.selectedIds[1]"
                        :localdata="item.options[1]"
                        @change="(val) => onSelectCoreExp(1, val, index)"
                        style="margin-bottom: 15rpx"
                        clear
                        placeholder="请选择维度"
                      />
                      <uni-data-select
                        v-model="item.selectedIds[2]"
                        :localdata="item.options[2]"
                        @change="(val) => onSelectCoreExp(2, val, index)"
                        style="margin-bottom: 15rpx"
                        clear
                        placeholder="请选择子维度"
                      />
                      <uni-data-select
                        v-model="item.selectedIds[3]"
                        :localdata="item.options[3]"
                        @change="(val) => onSelectCoreExp(3, val, index)"
                        clear
                        placeholder="请选择指标"
                      />
                    </template>
                  </view>
                  <view class="add_hxjy" @click="addCoreExperience">新增核心经验</view>
                </up-form-item>

                <!-- 教师支持 -->
                <up-form-item :style="detailFormItemStyle" label="教师支持" prop="teacherSupport">
                  <up-textarea
                    v-model="editingPlayDetail.teacherSupport"
                    placeholder="请输入教师支持"
                    border="none"
                    :cursorSpacing="100"
                    autoHeight
                    confirmType="none"
                  />
                </up-form-item>
              </up-form>
            </template>

            <!-- 非编辑模式下的显示 -->
            <template v-else>
              <view class="play-detail-item">
                <view class="play-detail-item-title">适合年龄段：</view>
                <view class="play-detail-item-content">
                  {{ currentPlayDetail.ageRange }}
                </view>
              </view>

              <view class="play-detail-item">
                <view class="play-detail-item-title">投放区域：</view>
                <view class="play-detail-item-content">
                  {{ currentPlayDetail.area }}
                </view>
              </view>

              <view class="play-detail-item">
                <view class="play-detail-item-title">适宜人数：</view>
                <view class="play-detail-item-content">
                  {{ currentPlayDetail.peopleCount }}
                </view>
              </view>

              <view class="play-detail-item">
                <view class="play-detail-item-title">结构化：</view>
                <view class="play-detail-item-content">
                  {{ currentPlayDetail.structure }}
                </view>
              </view>

              <!-- 玩法介绍 -->
              <view class="play-detail-item play-detail-intro">
                <view class="play-detail-item-title">玩法介绍：</view>
                <view class="play-detail-item-content">
                  {{ currentPlayDetail.introduction }}
                </view>
              </view>

              <!-- 核心经验 -->
              <view class="play-detail-item play-detail-experience">
                <view class="play-detail-item-title">核心经验：</view>
                <view class="play-detail-item-content">
                  <view
                    v-for="(exp, index) in currentPlayDetail.experiences"
                    :key="index"
                    class="experience-item"
                  >
                    <view class="experience-title">经验{{ index + 1 }}：</view>
                    <view class="experience-content">{{ exp }}</view>
                  </view>
                </view>
              </view>

              <!-- 教师支持 -->
              <view class="play-detail-item play-detail-teacher-support">
                <view class="play-detail-item-title">教师支持：</view>
                <view class="play-detail-item-content">
                  {{ currentPlayDetail.teacherSupport || '暂无' }}
                </view>
              </view>

              <!-- 幼儿兴趣 -->
              <!-- <view class="play-detail-item play-detail-interests" v-if="currentPlayDetail.isAdopted">
                <view class="play-detail-item-title">幼儿兴趣：</view>
                <view class="play-detail-item-content">
                  <view class="interests-content" v-if="currentPlayDetail.childInterests">
                    {{ currentPlayDetail.childInterests }}
                  </view>
                  <view class="interests-content" v-else>暂无选择</view>
                </view>
              </view> -->
            </template>
          </view>
        </scroll-view>

        <!-- 操作按钮区域 - 移到scroll-view外面 -->
        <!-- 操作按钮区域 - 仅AI推荐玩法显示 -->
        <view v-if="!currentPlayDetail.isAdopted" class="play-action-buttons ai-play-buttons">
          <button class="adopt-btn" @click="adoptPlay">采用</button>
          <button class="reject-btn" @click="rejectPlay">不采用</button>
        </view>

        <!-- 已采纳玩法的操作按钮 -->
        <view v-if="currentPlayDetail.isAdopted" class="play-action-buttons adopted-play-buttons">
          <button class="edit-btn" @click="toggleEditMode">
            {{ isEditMode ? '保存' : '编辑' }}
          </button>
          <button
            class="interest-btn"
            @click="openChildInterestPopup(currentPlayDetail.id, currentPlayDetail.childInterests)"
          >
            兴趣评定
          </button>
          <button class="delete-btn" @click="deletePlay">删除</button>
        </view>
      </view>
    </up-popup>

    <!-- 幼儿兴趣弹窗 -->
    <up-picker
      :show="showChildInterestPicker"
      :columns="childInterestColumns"
      @cancel="closeChildInterestPopup"
      @confirm="confirmChildInterestPicker"
      title="选择幼儿兴趣"
    ></up-picker>

    <!-- 材料分析编辑弹窗 -->
    <MaterialAnalysisEdit
      :show="showMaterialAnalysisEditPopup"
      :materialAnalysisData="materialAnalysisData"
      @close="closeEditPopup"
      @success="onMaterialAnalysisEditSuccess"
    />
  </BaseLayout>
</template>

<script setup>
import BaseLayout from '@/components/base-layout/base-layout.vue'
import { ref, reactive, computed, nextTick } from 'vue'
import {
  getMaterialAnalysis,
  getMaterialPlayList,
  getPlayDetail,
  adoptPlay as adoptPlayApi,
  deletePlay as deletePlayApi,
  saveMaterialInfo,
  getCombinedDetail,
  editMaterBaseInfo,
  getDictByCode,
  updatePlayChildInterest,
  updatePlayDetail,
  getClassMaterialArea,
  getCoreExperience
} from '@/api/game'
import { queryMaterialDetail, getMaterialAnalysisNew, getMaterialPlayListNew } from '../api/index.js'
import { onLoad } from '@dcloudio/uni-app'
import Upload from '@/components/Upload/Upload.vue'
import UploadNew from '@/components/UploadNew/UploadNew.vue'
import AIRecommendPopup from './AIRecommendPopup.vue'
import MaterialAnalysisEdit from './MaterialAnalysisEdit.vue'

const currentTab = ref(0)
// 材料id
const materialId = ref('')
const goback = () => {
  uni.navigateBack()
}

// 控制材料分析区域的展开/折叠状态
const isExpanded = ref(false)

// 切换展开/折叠状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}
// 已采用玩法
const playList = ref([])
// 当前选中的玩法
const currentPlay = ref(0)

// AI 推荐的玩法
const aiPlayList = ref([])
// 当前选中的 AI 推荐的玩法
const currentAiPlay = ref(0)

// AI推荐弹窗
const showAIRecommendPopup = ref(false)

// 打开AI推荐弹窗
const openAIRecommendPopup = () => {
  showAIRecommendPopup.value = true
}

// 关闭AI推荐弹窗
const closeAIRecommendPopup = () => {
  showAIRecommendPopup.value = false
}

// 处理AI生成成功
const handleAIGenerateSuccess = async () => {
  // 重新获取玩法列表
  playList.value = []
  aiPlayList.value = []
  await getPlayListFn(materialId.value, true)
}
// 详情类型
const detailType = ref('toy')

// 基本信息数据
const basicInfoData = ref({
  id: '',
  name: '',
  area: '',
  selectedImg: '',
  materialType: '',
  qualityLevel: '',
  safetyLevel: '',
  weightGram: ''
})

// 材料分析数据
const materialAnalysisData = ref({
  materialSummary: '',
  purpose: '',
  productionMethod: '',
  minAge: '',
  maxAge: '',
  suitablePeopleMin: '',
  suitablePeopleMax: ''
})

// 编辑基本信息表单数据
const editBasicInfoData = reactive({
  id: '',
  name: '',
  materialtype: '',
  qualitylevel: '',
  safetylevel: '',
  weightgram: '',
  selectedImg: '',
  productionmethod: '',
  area: '',
  combinedId: materialId.value
})



// 表单样式
const formItemStyle = {
  height: '115rpx',
  justifyContent: 'center'
}

// 用于文本区域的样式
const textareaFormItemStyle = {
  minHeight: '115rpx',
  justifyContent: 'flex-start',
  paddingTop: '20rpx'
}

// 品质和安全性字典选项
const qualityOptions = ref([])
const safetyOptions = ref([])
const productionMethodOptions = ref([])

// 改为使用picker
const showQualityPicker = ref(false)
const showSafetyPicker = ref(false)
const showChildInterestPicker = ref(false)
const showProductionMethodPicker = ref(false)

// 品质和安全性列数据
const qualityColumns = ref([])
const safetyColumns = ref([])
const childInterestColumns = ref([])
const productionMethodColumns = ref([])

// 保存原始字典数据，用于code和name的映射
const childInterestDict = ref([])
const productionMethodDict = ref([])
const qualityDict = ref([])
const safetyDict = ref([])

// 区域选择相关
const showRegionPicker = ref(false)
const regionNameList = ref([])
const tempRegionName = ref('')

// 区域数据格式化为picker需要的格式
const regionColumns = computed(() => {
  if (regionNameList.value.length === 0) return [[]]
  return [
    regionNameList.value.map((item) => {
      // 只有当areaAlias存在且不为空时才显示括号内容
      if (item.areaAlias) {
        return `${item.area}(${item.areaAlias})`
      } else {
        return item.area
      }
    })
  ]
})

// 获取制作方式名称
const getProductionMethodName = (code) => {
  if (!code) return ''
  const item = productionMethodDict.value.find((item) => item.dictItemCode === code)
  return item ? item.dictItemName : code
}

// 获取品质名称
const getQualityName = (code) => {
  if (!code) return ''
  const item = qualityDict.value.find((item) => item.dictItemCode === code)
  return item ? item.dictItemName : code
}

// 获取安全性名称
const getSafetyName = (code) => {
  if (!code) return ''
  const item = safetyDict.value.find((item) => item.dictItemCode === code)
  return item ? item.dictItemName : code
}

// 打开品质选择弹窗
const openQualityPopup = () => {
  qualityColumns.value = [qualityOptions.value.map((item) => item.name)]
  showQualityPicker.value = true
}

// 关闭品质选择弹窗
const closeQualityPopup = () => {
  showQualityPicker.value = false
}

// 打开安全性选择弹窗
const openSafetyPopup = () => {
  safetyColumns.value = [safetyOptions.value.map((item) => item.name)]
  showSafetyPicker.value = true
}

// 关闭安全性选择弹窗
const closeSafetyPopup = () => {
  showSafetyPicker.value = false
}

// 选择品质
const confirmQualityPicker = (e) => {
  const selectedName = e.value[0]
  // 通过选中的名称找到对应的code
  const selectedItem = qualityDict.value.find((item) => item.dictItemName === selectedName)

  if (selectedItem) {
    editBasicInfoData.qualitylevel = selectedItem.dictItemCode
  }
  closeQualityPopup()
}

// 选择安全性
const confirmSafetyPicker = (e) => {
  const selectedName = e.value[0]
  // 通过选中的名称找到对应的code
  const selectedItem = safetyDict.value.find((item) => item.dictItemName === selectedName)

  if (selectedItem) {
    editBasicInfoData.safetylevel = selectedItem.dictItemCode
  }
  closeSafetyPopup()
}

// 控制编辑弹窗显示
const showBasicInfoEditPopup = ref(false)
const showMaterialAnalysisEditPopup = ref(false)
const showBookEditPopup = ref(false)

// 图书编辑数据结构
const editBookInfoData = reactive({
  id: '',
  name: '',
  selectedImg: '',
  area: '',
  areaId: '',
  sourceType: 'book'
})

// 打开编辑弹窗
const openEditPopup = async (type) => {
  if (type === 'basicInfo' && detailType.value === 'book') {
    // 图书编辑逻辑
    editBookInfoData.id = basicInfoData.value.id
    editBookInfoData.name = basicInfoData.value.name
    // 确保图片数据正确传递，处理空值情况
    editBookInfoData.selectedImg = basicInfoData.value.selectedImg || ''
    editBookInfoData.area = basicInfoData.value.area
    editBookInfoData.areaId = basicInfoData.value.areaId || ''

    // 调试输出，确认数据正确性
    console.log('图书编辑弹窗数据:', {
      id: editBookInfoData.id,
      name: editBookInfoData.name,
      selectedImg: editBookInfoData.selectedImg,
      area: editBookInfoData.area,
      basicInfoDataSelectedImg: basicInfoData.value.selectedImg
    })

    // 获取区域列表
    const userInfo = uni.getStorageSync('USER_INFO')
    const classId = userInfo.currentClassId || userInfo.classIds[0]
    const areaRes = await getClassMaterialArea({
      schoolId: userInfo.currentSchoolId,
      classId: classId
    })
    if (areaRes && areaRes.data) {
      regionNameList.value = areaRes.data
      // 查找当前区域，设置显示名称
      const currentArea = regionNameList.value.find(
        (item) => item.area === basicInfoData.value.area
      )
      if (currentArea && currentArea.areaAlias) {
        tempRegionName.value = `${currentArea.area}(${currentArea.areaAlias})`
      } else {
        tempRegionName.value = basicInfoData.value.area
      }
    }

    showBookEditPopup.value = true
  } else if (type === 'basicInfo') {
    // 玩具编辑逻辑（原有逻辑）
    // 回填基本信息数据 - 确保正确映射属性名
    editBasicInfoData.id = basicInfoData.value.id
    editBasicInfoData.name = basicInfoData.value.name
    editBasicInfoData.materialtype = basicInfoData.value.materialType
    editBasicInfoData.qualitylevel = basicInfoData.value.qualityLevel
    editBasicInfoData.safetylevel = basicInfoData.value.safetyLevel
    editBasicInfoData.weightgram = basicInfoData.value.weightGram
    editBasicInfoData.selectedImg = basicInfoData.value.selectedImg
    editBasicInfoData.area = basicInfoData.value.area
    editBasicInfoData.combinedId = materialId.value
    // 设置区域显示名称
    tempRegionName.value = basicInfoData.value.area

    // 获取区域列表
    const userInfo = uni.getStorageSync('USER_INFO')
    const classId = userInfo.currentClassId || userInfo.classIds[0]
    const areaRes = await getClassMaterialArea({
      schoolId: userInfo.currentSchoolId,
      classId: classId
    })
    if (areaRes && areaRes.data) {
      regionNameList.value = areaRes.data
      // 去除['全部']
      regionNameList.value = regionNameList.value.filter((item) => item.area !== '全部')
      // 查找当前区域，设置显示名称
      const currentArea = regionNameList.value.find(
        (item) => item.area === basicInfoData.value.area
      )
      if (currentArea && currentArea.areaAlias) {
        tempRegionName.value = `${currentArea.area}(${currentArea.areaAlias})`
      }
    }

    // 制作方式字段可能存储为code或name，暂时使用原值，稍后会根据字典数据进行更新
    editBasicInfoData.productionmethod = basicInfoData.value.productionMethod

    // 获取品质 和 安全性 字典
    const res1 = await getDictByCode({
      code: 'materialQuality'
    })
    const res2 = await getDictByCode({
      code: 'materialSafety'
    })
    const res3 = await getDictByCode({
      code: 'productionMethod'
    })

    // 存储字典数据
    if (res1 && res1.data) {
      qualityDict.value = res1.data
      qualityOptions.value = res1.data.map((item) => ({
        id: item.dictItemCode,
        name: item.dictItemName
      }))
      // 准备picker列数据
      qualityColumns.value = [res1.data.map((item) => item.dictItemName)]

      // 检查当前的qualitylevel是否是name而不是code，如果是就转换为code
      const currentQuality = editBasicInfoData.qualitylevel
      if (currentQuality) {
        // 尝试查找是否有匹配的name
        const matchByName = qualityDict.value.find((item) => item.dictItemName === currentQuality)
        if (matchByName) {
          // 如果找到匹配的name，则转换为code
          editBasicInfoData.qualitylevel = matchByName.dictItemCode
        } else {
          // 尝试查找是否有匹配的code
          const matchByCode = qualityDict.value.find((item) => item.dictItemCode === currentQuality)
          if (!matchByCode) {
            // 如果既不是name也不是code，则清空
            editBasicInfoData.qualitylevel = ''
          }
        }
      }
    }
    if (res2 && res2.data) {
      safetyDict.value = res2.data
      safetyOptions.value = res2.data.map((item) => ({
        id: item.dictItemCode,
        name: item.dictItemName
      }))
      // 准备picker列数据
      safetyColumns.value = [res2.data.map((item) => item.dictItemName)]

      // 检查当前的safetylevel是否是name而不是code，如果是就转换为code
      const currentSafety = editBasicInfoData.safetylevel
      if (currentSafety) {
        // 尝试查找是否有匹配的name
        const matchByName = safetyDict.value.find((item) => item.dictItemName === currentSafety)
        if (matchByName) {
          // 如果找到匹配的name，则转换为code
          editBasicInfoData.safetylevel = matchByName.dictItemCode
        } else {
          // 尝试查找是否有匹配的code
          const matchByCode = safetyDict.value.find((item) => item.dictItemCode === currentSafety)
          if (!matchByCode) {
            // 如果既不是name也不是code，则清空
            editBasicInfoData.safetylevel = ''
          }
        }
      }
    }
    if (res3 && res3.data) {
      productionMethodDict.value = res3.data
      productionMethodOptions.value = res3.data.map((item) => ({
        id: item.dictItemCode,
        name: item.dictItemName
      }))
      // 准备picker列数据
      productionMethodColumns.value = [res3.data.map((item) => item.dictItemName)]

      // 检查当前的productionmethod是否是name而不是code，如果是就转换为code
      const currentMethod = editBasicInfoData.productionmethod
      if (currentMethod) {
        // 尝试查找是否有匹配的name
        const matchByName = productionMethodDict.value.find(
          (item) => item.dictItemName === currentMethod
        )
        if (matchByName) {
          // 如果找到匹配的name，则转换为code
          editBasicInfoData.productionmethod = matchByName.dictItemCode
        } else {
          // 尝试查找是否有匹配的code
          const matchByCode = productionMethodDict.value.find(
            (item) => item.dictItemCode === currentMethod
          )
          if (!matchByCode) {
            // 如果既不是name也不是code，则清空
            editBasicInfoData.productionmethod = ''
          }
        }
      }
    }

    showBasicInfoEditPopup.value = true
  } else if (type === 'materialAnalysis') {
    // 材料分析编辑逻辑
    showMaterialAnalysisEditPopup.value = true
  }
}

// 关闭编辑弹窗
const closeEditPopup = () => {
  showBasicInfoEditPopup.value = false
  showMaterialAnalysisEditPopup.value = false
  showBookEditPopup.value = false
}

// 材料分析编辑成功回调
const onMaterialAnalysisEditSuccess = async (formData) => {
  // 更新本地数据
  Object.assign(materialAnalysisData.value, formData)

  // 重新获取材料分析数据以确保数据同步
  if (materialId.value) {
    await getPlayAnalysisFn(materialId.value)
  }
}

// 保存编辑内容
const saveEdit = async (type) => {
  if (type === 'basicInfo' && detailType.value === 'book') {
    // 保存图书基本信息编辑
    try {
      // 查找当前选中区域的 areaId
      let areaId = editBookInfoData.areaId
      if (!areaId && editBookInfoData.area) {
        const currentArea = regionNameList.value.find((item) => item.area === editBookInfoData.area)
        if (currentArea) {
          areaId = currentArea.id
        }
      }

      const res = await editMaterBaseInfo({
        id: editBookInfoData.id,
        area: editBookInfoData.area,
        sourceType: editBookInfoData.sourceType,
        areaId: areaId,
        name: editBookInfoData.name,
        selectedImg: editBookInfoData.selectedImg
      })

      if (res.status === 0) {
        uni.$u.toast('保存成功')

        // 重新获取页面数据，刷新UI显示
        const detailRes = await getCombinedDetail({
          combinedId: materialId.value,
          sourceType: detailType.value
        })
        if (detailRes && detailRes.data) {
          basicInfoData.value = detailRes.data
        }

        showBookEditPopup.value = false
      } else {
        uni.$u.toast(res.message || '保存失败')
      }
    } catch (error) {
      console.error('保存图书信息失败', error)
      uni.$u.toast('保存失败')
    }
  } else if (type === 'basicInfo') {
    // 保存玩具基本信息编辑（原有逻辑）
    // 查找当前选中区域的 areaId
    let areaId = editBasicInfoData.areaId
    if (!areaId && editBasicInfoData.area) {
      const currentArea = regionNameList.value.find((item) => item.area === editBasicInfoData.area)
      if (currentArea) {
        areaId = currentArea.id
      }
    }

    const res = await editMaterBaseInfo({
      id: basicInfoData.value.id,
      name: editBasicInfoData.name,
      area: editBasicInfoData.area,
      // 如果区域切换了要加上区域 id
      areaId: areaId,
      materialtype: editBasicInfoData.materialtype,
      qualitylevel: editBasicInfoData.qualitylevel,
      safetylevel: editBasicInfoData.safetylevel,
      weightgram: editBasicInfoData.weightgram,
      selectedImg: editBasicInfoData.selectedImg,
      productionmethod: editBasicInfoData.productionmethod,
      combinedId: materialId.value,
      combinedSelectedImg: editBasicInfoData.selectedImg,
      materialAnalysisId: materialAnalysisData.value.id,
      taobaoId: basicInfoData.value.taobaoId,
      classId: basicInfoData.value.classId
    })
    if (res.status === 0) {
      uni.$u.toast('保存成功')

      // 重新获取页面数据，刷新UI显示
      const detailRes = await getCombinedDetail({
        combinedId: materialId.value,
        sourceType: detailType.value
      })
      if (detailRes && detailRes.data) {
        basicInfoData.value = detailRes.data
      }
    } else {
      uni.$u.toast(res.message)
    }

    showBasicInfoEditPopup.value = false
  }
}
// 获取玩法分析（根据来源切换接口）
const getPlayAnalysisFn = async (id, fromRecommend = false) => {
  try {
    const apiCall = fromRecommend ? getMaterialAnalysisNew : getMaterialAnalysis
    const res = await apiCall({
      combinedId: id
    })
    if (res && res.data) {
      materialAnalysisData.value = res.data
    }
  } catch (error) {
    console.error('获取材料分析失败', error)
  }
}
// 获取组合玩法（根据来源切换接口）
const getPlayListFn = async (id, fromRecommend = false) => {
  try {
    const apiCall = fromRecommend ? getMaterialPlayListNew : getMaterialPlayList
    const res = await apiCall({
      combinedId: id
    })

    if (res && res.data) {
      res.data.forEach((item) => {
        if (item.playAdopt === 0) {
          aiPlayList.value.push(item)
        } else if (item.playAdopt === 1) {
          playList.value.push(item)
        }
      })
    }
  } catch (error) {
    console.error('获取玩法列表失败', error)
  }
}

// 玩法详情弹窗
const showPlayDetailPopup = ref(false)
const currentPlayDetail = ref({
  title: '',
  isAdopted: false,
  ageRange: '',
  area: '',
  peopleCount: '',
  structure: '',
  introduction: '',
  experiences: [],
  childInterests: '',
  teacherSupport: ''
})

// 编辑模式状态
const isEditMode = ref(false)

// 临时存储编辑中的数据
const editingPlayDetail = ref({})

// 切换编辑模式
const toggleEditMode = async () => {
  if (isEditMode.value) {
    // 当前是编辑模式，点击即保存
    try {
      uni.showLoading({
        title: '保存中...'
      })

      // 获取核心经验参数
      const coreExpParams = getCoreExperienceParams()
      if (!coreExpParams) {
        uni.hideLoading()
        return
      }

      // 准备更新数据
      const updateData = {
        id: currentPlayDetail.value.id,
        playName: editingPlayDetail.value.title,
        playIntro: editingPlayDetail.value.introduction,
        area: editingPlayDetail.value.area,
        // 解析年龄范围
        ageMin: editingPlayDetail.value.ageRange.split(' - ')[0],
        ageMax: editingPlayDetail.value.ageRange.split(' - ')[1],
        // 解析适宜人数
        suitablePeopleMin: editingPlayDetail.value.peopleCount.split('-')[0],
        suitablePeopleMax: editingPlayDetail.value.peopleCount.split('-')[1].replace('人', ''),
        // 结构化
        structured: editingPlayDetail.value.structure,
        // 核心经验参数
        coreExperienceId: coreExpParams.coreExperienceId,
        coreExperience: coreExpParams.coreExperience,
        // 教师支持
        teacherSupport: editingPlayDetail.value.teacherSupport
      }

      // 调用更新API
      const res = await updatePlayDetail(updateData)

      if (res && res.status === 0) {
        // 更新成功后，将编辑的数据赋值给当前显示的数据
        Object.assign(currentPlayDetail.value, editingPlayDetail.value)

        uni.hideLoading()
        uni.$u.toast('保存成功')
        isEditMode.value = false
        closePlayDetailPopup()
      } else {
        uni.hideLoading()
        uni.$u.toast(res.message || '保存失败，请重试')
      }
    } catch (error) {
      uni.hideLoading()
      uni.$u.toast('保存失败，请重试')
      console.error('保存玩法详情失败', error)
    }
  } else {
    // 进入编辑模式，拷贝当前数据用于编辑
    editingPlayDetail.value = JSON.parse(JSON.stringify(currentPlayDetail.value))
    isEditMode.value = true

    // 初始化核心经验数据
    coreExperienceList.value = []

    // 加载第一级选项，然后初始化已有的核心经验
    loadFirstLevelOptionsAndInitExistingExperiences()
  }
}

// 加载第一级核心经验选项并初始化已有的核心经验
async function loadFirstLevelOptionsAndInitExistingExperiences() {
  try {
    // 加载领域（第一级）选项
    const res = await getCoreExperience(0, 1)
    if (res && res.data) {
      const firstLevelOptions = res.data.map((item) => ({
        value: item.id,
        text: item.title
      }))

      // 如果有已存在的核心经验，先显示一个"已有经验"区域
      if (currentPlayDetail.value.experiences && currentPlayDetail.value.experiences.length > 0) {
        // 显示已有的核心经验，允许删除
        currentPlayDetail.value.experiences.forEach((exp, index) => {
          // 添加一个显示已有经验的项，可以删除
          addExistingExperience(exp, index)
        })
      }

      // 添加一个新的空白核心经验项，用于添加新的经验
      addCoreExperience()
    }
  } catch (error) {
    console.error('加载核心经验选项失败', error)
    uni.$u.toast('加载核心经验选项失败')
  }
}

// 添加已有的核心经验
function addExistingExperience(experienceText, index) {
  coreExperienceList.value.push({
    isExisting: true,
    experienceText: experienceText,
    experienceIndex: index,
    selectedIds: ['', '', '', ''],
    selectedTitles: ['', '', '', ''],
    options: [[], [], [], []]
  })
}

// 打开玩法详情弹窗
const openPlayDetailPopup = async (isAdopted, index) => {
  // 重置编辑模式
  isEditMode.value = false

  // 获取对应的玩法ID
  const playData = isAdopted ? playList.value[index] : aiPlayList.value[index]

  try {
    uni.showLoading({
      title: '加载中...'
    })

    // 先获取幼儿兴趣字典
    const interestDictRes = await getDictByCode({
      code: 'childInterest'
    })

    if (interestDictRes && interestDictRes.data) {
      childInterestDict.value = interestDictRes.data
      // 准备picker列数据 - 展示名称
      childInterestColumns.value = [interestDictRes.data.map((item) => item.dictItemName)]
    }

    // 调用接口获取玩法详情
    const res = await getPlayDetail({
      dataType: 'teacher',
      playId: playData.id
    })

    uni.hideLoading()

    // 设置当前玩法详情数据
    currentPlayDetail.value = {
      id: res.data.id,
      title: res.data.playName,
      isAdopted: isAdopted, // 用传入的参数标识是否已采纳
      ageRange: `${res.data.ageMin} - ${res.data.ageMax}`,
      area: res.data.area,
      peopleCount: `${res.data.suitablePeopleMin}-${res.data.suitablePeopleMax}人`,
      structure: res.data.structured,
      introduction: res.data.playIntro,
      experiences: res.data.coreExperience
        ? typeof res.data.coreExperience === 'string' && res.data.coreExperience.startsWith('[')
          ? JSON.parse(res.data.coreExperience)
          : res.data.coreExperience.split(';')
        : [],
      childInterests: res.data.childInterest
        ? childInterestDict.value.find((item) => item.dictItemCode == res.data.childInterest)
            ?.dictItemName || res.data.childInterest
        : '',
      teacherSupport: res.data.teacherSupport || ''
    }

    // 显示弹窗
    showPlayDetailPopup.value = true
  } catch (error) {
    uni.hideLoading()
    uni.$u.toast('获取玩法详情失败')
    console.error('获取玩法详情失败', error)
  }
}

// 关闭玩法详情弹窗
const closePlayDetailPopup = () => {
  showPlayDetailPopup.value = false
  // 重置编辑模式
  isEditMode.value = false
}

// 删除已采纳的玩法
const deletePlay = async () => {
  // 显示确认弹窗
  uni.showModal({
    title: '确认删除',
    content: '确定要删除该玩法吗？删除后无法恢复',
    confirmColor: '#DD6161',
    success: async (res) => {
      if (res.confirm) {
        // 用户点击确认，继续删除操作
        try {
          uni.showLoading({
            title: '删除中...'
          })

          // 获取 schoolId 和 classId
          const userInfo = uni.getStorageSync('USER_INFO')
          const schoolId = uni.getStorageSync('CURRENT_SCHOOL_ID') || userInfo.currentSchoolId
          const classId = userInfo.currentClassId || userInfo.classIds[0]

          await deletePlayApi({
            playId: currentPlayDetail.value.id,
            dataType: 'teacher',
            combinedId: materialId.value,
            schoolId: schoolId,
            classId: classId
          })

          uni.hideLoading()
          uni.$u.toast('玩法已删除')

          // 从已采纳列表中移除该玩法
          const index = playList.value.findIndex((item) => item.id === currentPlayDetail.value.id)
          if (index !== -1) {
            playList.value.splice(index, 1)
          }

          closePlayDetailPopup()
        } catch (error) {
          uni.hideLoading()
          uni.$u.toast('删除失败，请重试')
          console.error('删除玩法失败', error)
        }
      }
      // 如果用户点击取消，则不执行任何操作
    }
  })
}

// 采用AI推荐的玩法
const adoptPlay = async () => {
  try {
    uni.showLoading({
      title: '处理中...'
    })

    // 获取 schoolId 和 classId
    const userInfo = uni.getStorageSync('USER_INFO')
    const schoolId = uni.getStorageSync('CURRENT_SCHOOL_ID') || userInfo.currentSchoolId
    const classId = userInfo.currentClassId || userInfo.classIds[0]

    await adoptPlayApi({
      playId: currentPlayDetail.value.id,
      isAdopt: 1, // 1表示采纳
      dataType: 'teacher',
      combinedId: materialId.value,
      schoolId: schoolId,
      classId: classId
    })

    uni.hideLoading()
    uni.$u.toast('已采用该玩法')

    // 从AI推荐列表移动到已采纳列表
    const index = aiPlayList.value.findIndex((item) => item.id === currentPlayDetail.value.id)
    if (index !== -1) {
      const adoptedPlay = aiPlayList.value.splice(index, 1)[0]
      adoptedPlay.playAdopt = 1 // 更新状态为已采纳
      playList.value.push(adoptedPlay)
    }

    closePlayDetailPopup()
  } catch (error) {
    uni.hideLoading()
    uni.$u.toast('采用失败，请重试')
    console.error('采用玩法失败', error)
  }
}

// 不采用AI推荐的玩法
const rejectPlay = async () => {
  try {
    uni.showLoading({
      title: '处理中...'
    })

    // 获取 schoolId 和 classId
    const userInfo = uni.getStorageSync('USER_INFO')
    const schoolId = uni.getStorageSync('CURRENT_SCHOOL_ID') || userInfo.currentSchoolId
    const classId = userInfo.currentClassId || userInfo.classIds[0]

    await adoptPlayApi({
      playId: currentPlayDetail.value.id,
      isAdopt: -1, // -1表示不采纳
      dataType: 'teacher',
      combinedId: materialId.value,
      schoolId: schoolId,
      classId: classId
    })

    // 重新获取玩法列表
    aiPlayList.value = [] // 清空AI推荐列表
    playList.value = [] // 清空已采纳列表

    await getPlayListFn(materialId.value, true)

    uni.hideLoading()
    uni.$u.toast('已拒绝该玩法')
    closePlayDetailPopup()
  } catch (error) {
    uni.hideLoading()
    uni.$u.toast('操作失败，请重试')
    console.error('拒绝玩法失败', error)
  }
}



// 幼儿兴趣数据和控制
const childInterestOptions = ref([])
const showChildInterestPopup = ref(false)
const currentPlayId = ref('')

// 打开幼儿兴趣选择弹窗
const openChildInterestPopup = (playId, currentInterests) => {
  currentPlayId.value = playId
  // 这里不需要处理 selectedChildInterests，picker 会自动管理所选项
  showChildInterestPicker.value = true
}

// 关闭幼儿兴趣选择弹窗
const closeChildInterestPopup = () => {
  showChildInterestPicker.value = false
}

// 确认幼儿兴趣选择
const confirmChildInterestPicker = async (e) => {
  try {
    uni.showLoading({
      title: '保存中...'
    })

    const selectedName = e.value[0]
    // 通过选中的名称找到对应的code
    const selectedItem = childInterestDict.value.find((item) => item.dictItemName === selectedName)

    if (!selectedItem) {
      uni.hideLoading()
      uni.$u.toast('数据异常，请重试')
      return
    }

    const selectedCode = selectedItem.dictItemCode

    await updatePlayChildInterest({
      playId: currentPlayId.value,
      childInterest: selectedCode
    })

    uni.hideLoading()
    uni.$u.toast('保存成功')
    // 关闭幼儿兴趣选择弹窗
    closeChildInterestPopup()

    // 更新当前显示的数据 - 在界面上显示名称
    if (currentPlayDetail.value.id === currentPlayId.value) {
      currentPlayDetail.value.childInterests = selectedName
    }

    // 重置玩法列表并重新获取，以刷新兴趣度显示
    playList.value = []
    aiPlayList.value = []
    await getPlayListFn(materialId.value, true)
  } catch (error) {
    uni.hideLoading()
    uni.$u.toast('保存失败，请重试')
    console.error('保存幼儿兴趣失败', error)
  }
}

// 图片预览功能
const previewImage = (current, urls) => {
  uni.previewImage({
    current, // 当前显示图片的链接
    urls, // 需要预览的图片链接列表
    indicator: 'default',
    loop: true
  })
}

// 移动图片位置
const moveImage = (index, direction) => {
  const images = editBasicInfoData.selectedImg.split(',')
  if (direction === 'up' && index > 0) {
    ;[images[index], images[index - 1]] = [images[index - 1], images[index]]
  } else if (direction === 'down' && index < images.length - 1) {
    ;[images[index], images[index + 1]] = [images[index + 1], images[index]]
  }
  editBasicInfoData.selectedImg = images.join(',')
}

// 删除图片
const deleteImage = (index) => {
  //弹窗
  uni.showModal({
    title: '提示',
    content: '确定删除该图片吗？',
    success: (res) => {
      if (res.confirm) {
        const images = editBasicInfoData.selectedImg.split(',')
        images.splice(index, 1)
        editBasicInfoData.selectedImg = images.join(',')
      }
    }
  })
}

// 图书编辑相关函数
// 移动图书图片顺序
const moveBookImage = (index, direction) => {
  if (!editBookInfoData.selectedImg) return

  const images = editBookInfoData.selectedImg.split(',').filter((img) => img.trim())
  if (direction === 'up' && index > 0) {
    ;[images[index], images[index - 1]] = [images[index - 1], images[index]]
  } else if (direction === 'down' && index < images.length - 1) {
    ;[images[index], images[index + 1]] = [images[index + 1], images[index]]
  }
  editBookInfoData.selectedImg = images.join(',')
}

// 删除图书图片
const deleteBookImage = (index) => {
  uni.showModal({
    title: '提示',
    content: '确定删除该图片吗？',
    success: (res) => {
      if (res.confirm) {
        if (!editBookInfoData.selectedImg) return

        const images = editBookInfoData.selectedImg.split(',').filter((img) => img.trim())
        images.splice(index, 1)
        editBookInfoData.selectedImg = images.join(',')
      }
    }
  })
}

// 图书图片上传回调
const bookUploadCallback = (list) => {
  // 将新上传的图片添加到现有图片列表中
  const currentImages = editBookInfoData.selectedImg
    ? editBookInfoData.selectedImg.split(',').filter((img) => img)
    : []
  const newImages = list.map((item) => item.serviceUri || item.url || item.path).filter(Boolean)
  const allImages = [...currentImages, ...newImages]
  editBookInfoData.selectedImg = allImages.join(',')
}

// 图书图片删除回调（这个函数可能不会被直接调用，因为我们使用的是自定义删除按钮）
const bookDelFile = (item, index) => {
  const images = editBookInfoData.selectedImg.split(',').filter((img) => img)
  images.splice(index, 1)
  editBookInfoData.selectedImg = images.join(',')
}

// 制作方式选择弹窗
// const showProductionMethodPicker = ref(false)
// const productionMethodColumns = ref([])

// 打开制作方式选择弹窗
const openProductionMethodPopup = () => {
  productionMethodColumns.value = [productionMethodOptions.value.map((item) => item.name)]
  showProductionMethodPicker.value = true
}

// 关闭制作方式选择弹窗
const closeProductionMethodPopup = () => {
  showProductionMethodPicker.value = false
}

// 选择制作方式
const confirmProductionMethodPicker = (e) => {
  const selectedName = e.value[0]
  // 通过选中的名称找到对应的code
  const selectedItem = productionMethodDict.value.find((item) => item.dictItemName === selectedName)

  if (selectedItem) {
    editBasicInfoData.productionmethod = selectedItem.dictItemCode
  }
  closeProductionMethodPopup()
}

onLoad(async (options) => {
  detailType.value = options.sourceType
  try {
    let res
    // 根据来源判断使用哪个接口获取基本信息
    if (options.from === 'recommend') {
      // 从采购材料推荐页面跳转，使用 queryMaterialDetail 接口
      res = await queryMaterialDetail({
        id: options.id,
        sourceType: 'toy'
      })
    } else {
      // 其他来源，使用 getCombinedDetail 接口
      res = await getCombinedDetail({
        combinedId: options.id,
        sourceType: options.sourceType
      })
    }

    if (res && res.data) {
      console.log('API返回数据:', res.data)
      basicInfoData.value = res.data
      // 材料id
      materialId.value = options.id

      if (materialId.value) {
        // 如果是从推荐页面来的，其他详情信息仍使用 getCombinedDetail 接口
        if (options.from === 'recommend') {
          // 获取其他详情信息
          const detailRes = await getCombinedDetail({
            combinedId: options.id,
            sourceType: options.sourceType
          })
          if (detailRes && detailRes.data) {
            // 只更新非基本信息的部分，保留基本信息
            const basicInfo = basicInfoData.value
            basicInfoData.value = { ...detailRes.data, ...basicInfo }
          }
        }

        getPlayAnalysisFn(materialId.value, options.from === 'recommend')
        getPlayListFn(materialId.value, options.from === 'recommend')

        // 获取幼儿兴趣字典
        const interestDictRes = await getDictByCode({
          code: 'childInterest'
        })

        if (interestDictRes && interestDictRes.data) {
          childInterestDict.value = interestDictRes.data
          // 准备picker列数据 - 展示名称
          childInterestColumns.value = [interestDictRes.data.map((item) => item.dictItemName)]
        }
      }
    }
  } catch (error) {
    console.error('获取材料详情失败', error)
  }
})

// 打开区域选择弹窗
const openRegionPopup = () => {
  showRegionPicker.value = true
}

// 关闭区域选择弹窗
const closeRegionPopup = () => {
  showRegionPicker.value = false
}

// 确认区域选择
const confirmRegion = (e) => {
  const selectedValue = e.value[0]
  // 查找匹配的区域对象，需要处理有areaAlias和没有areaAlias的情况
  const selectedRegionObj = regionNameList.value.find((item) => {
    if (item.areaAlias) {
      return `${item.area}(${item.areaAlias})` === selectedValue
    } else {
      return item.area === selectedValue
    }
  })

  if (selectedRegionObj) {
    // 根据当前弹窗类型更新不同的数据
    if (showBookEditPopup.value) {
      // 图书编辑
      editBookInfoData.area = selectedRegionObj.area
      editBookInfoData.areaId = selectedRegionObj.id
    } else {
      // 玩具编辑
      editBasicInfoData.area = selectedRegionObj.area
      editBasicInfoData.areaId = selectedRegionObj.id
    }

    // 更新显示内容
    tempRegionName.value = selectedRegionObj.areaAlias
      ? `${selectedRegionObj.area}(${selectedRegionObj.areaAlias})`
      : selectedRegionObj.area
  }
  showRegionPicker.value = false
}

// 参考add.vue中的图片上传逻辑
// const attachmentResourceIds = ref([])

// 处理上传回调
const uploadCallback = (list) => {
  // 我们不累积attachmentResourceIds，因为我们不想在上传组件中显示已上传图片
  // attachmentResourceIds.value = list

  // 将所有图片的URL用逗号拼接
  if (list && list.length > 0) {
    const urls = list.map((item) => item.serviceUri || item.url || item.path).filter(Boolean)

    // 将新上传的图片添加到已有图片列表中
    const currentImages = editBasicInfoData.selectedImg
      ? editBasicInfoData.selectedImg.split(',')
      : []

    // 合并时去除重复的图片URL
    const allUrls = [...currentImages, ...urls]
    const uniqueUrls = [...new Set(allUrls)]
    editBasicInfoData.selectedImg = uniqueUrls.join(',')
  }
}

// 处理删除文件
const delFile = (item, index) => {
  // 因为我们不使用attachmentResourceIds来存储和显示已上传图片，所以这个函数基本上不会被调用
  console.log('删除文件', item, index)
}

// 滚动位置控制
const scrollTop = ref(0)
const detailScrollView = ref(null)
const textareaPositions = reactive({})

// 处理文本框获取焦点事件
const handleTextareaFocus = (event, id) => {
  // 获取当前文本框在页面中的位置
  const query = uni.createSelectorQuery()
  query
    .select(`.editable-textarea`)
    .boundingClientRect((data) => {
      if (data) {
        // 存储各个文本框的位置信息
        textareaPositions[id] = data

        // 计算键盘高度 (预估值，可根据实际情况调整)
        const keyboardHeight = uni.getSystemInfoSync().windowHeight * 0.4

        // 计算文本框底部到屏幕底部的距离
        const distanceToBottom = uni.getSystemInfoSync().windowHeight - data.bottom

        // 如果距离小于键盘高度，需要滚动
        if (distanceToBottom < keyboardHeight) {
          // 计算需要滚动的距离
          const scrollDistance = keyboardHeight - distanceToBottom + 20 // 额外加点空间

          // 设置滚动位置
          setTimeout(() => {
            scrollTop.value = scrollTop.value + scrollDistance
          }, 300)
        }
      }
    })
    .exec()
}

// 玩法详情表单样式
const detailFormItemStyle = `
  border-radius: 16rpx;
  padding: 10rpx 20rpx;
  margin-bottom: 20rpx;
  background: #f9f9f9;
`

// 根据code获取幼儿兴趣名称
const getChildInterestName = (code) => {
  if (!code || !childInterestDict.value || childInterestDict.value.length === 0) return ''
  const interest = childInterestDict.value.find((item) => item.dictItemCode == code)
  return interest ? interest.dictItemName : ''
}

// 根据名称获取幼儿兴趣code
const getInterestCodeByName = (name) => {
  if (!name || !childInterestDict.value || childInterestDict.value.length === 0) return ''
  const interest = childInterestDict.value.find((item) => item.dictItemName === name)
  return interest ? interest.dictItemCode : ''
}

// 核心经验多级联动数据
const coreExperienceList = ref([])

// 新增一条核心经验
function addCoreExperience() {
  coreExperienceList.value.push({
    selectedIds: ['', '', '', ''],
    selectedTitles: ['', '', '', ''],
    options: [[], [], [], []]
  })
  loadOptions(0, 1, coreExperienceList.value.length - 1, 0)
}

// 级联选择逻辑
async function onSelectCoreExp(level, value, expIndex) {
  // value 其实是选中的id
  const item = coreExperienceList.value[expIndex]
  // 找到完整对象
  const option = item.options[level].find((opt) => opt.value === value)
  item.selectedIds[level] = value
  item.selectedTitles[level] = option ? option.text : ''
  // 清空后面
  for (let i = level + 1; i < 4; i++) {
    item.selectedIds[i] = ''
    item.selectedTitles[i] = ''
    item.options[i] = []
  }
  if (level < 3) {
    await loadOptions(value, level + 2, expIndex, level + 1)
  }
}

// 加载下拉数据
async function loadOptions(parentId, depth, expIndex, optionIndex) {
  try {
    const res = await getCoreExperience(parentId, depth)
    coreExperienceList.value[expIndex].options[optionIndex] = (res.data || []).map((item) => ({
      value: item.id,
      text: item.title
    }))
  } catch (e) {
    uni.$u.toast('获取下拉数据失败')
  }
}

// 删除一条核心经验
function removeCoreExperience(index) {
  uni.showModal({
    title: '提示',
    content: '确定删除该核心经验吗？',
    success: (res) => {
      if (res.confirm) {
        coreExperienceList.value.splice(index, 1)
      }
    }
  })
}

// 保存时组装参数
function getCoreExperienceParams() {
  // 收集已有的核心经验
  const existingExperiences = coreExperienceList.value
    .filter((item) => item.isExisting)
    .map((item) => item.experienceText)

  // 验证新增的核心经验是否填写完整
  const newExperienceItems = coreExperienceList.value.filter((item) => !item.isExisting)
  // 只验证用户实际开始填写的新增核心经验
  const filledNewExperienceItems = newExperienceItems.filter((item) =>
    item.selectedIds.some((id) => id !== '')
  )

  for (const item of filledNewExperienceItems) {
    if (item.selectedIds.some((id) => !id)) {
      uni.$u.toast('请完整选择每条新增核心经验')
      return null
    }
  }

  // 收集新增的核心经验ID
  const newExperienceIds = newExperienceItems.map((item) => item.selectedIds[3])

  // 收集新增的核心经验文本
  const newExperienceTexts = newExperienceItems.map((item) => item.selectedTitles.join('-'))

  // 合并已有和新增的核心经验
  const allExperienceTexts = [...existingExperiences, ...newExperienceTexts]

  // 如果没有任何核心经验，返回空数组的JSON字符串
  if (allExperienceTexts.length === 0) {
    return {
      coreExperienceId: '',
      coreExperience: '[]'
    }
  }

  // 返回JSON字符串格式的核心经验
  return {
    coreExperienceId: newExperienceIds.join(','),
    coreExperience: JSON.stringify(allExperienceTexts)
  }
}
</script>

<style lang="scss" scoped>
@import './style.scss';
</style>
